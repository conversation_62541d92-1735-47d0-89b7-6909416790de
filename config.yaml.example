# PixivTagDownloader Configuration File
# Copy this file to config.yaml and modify as needed

# Authentication settings
# REQUIRED: Your Pixiv session cookie
# To get your cookie:
# 1. Login to Pixiv in your browser
# 2. Open browser developer tools (F12)
# 3. Go to Application/Storage -> Cookies -> https://www.pixiv.net
# 4. Copy the entire cookie string
cookie: "your_pixiv_cookie_here"

# Output directory settings
output_dir: "Output"

# Path template settings
# Available variables:
# {uid}, {username}, {pid}, {title}, {type}, {page_index}, {page_count}
# {series_title}, {series_id}, {upload_date}, {tags}, {r18}
# {like_count}, {bookmark_count}, {ext}
path_template:
  directory: "{uid}_{username}/{type}/{series_title}"
  filename: "{upload_date}_{pid}_{title}{page_index}"
  page_index_format: "p{:02d}"  # p00, p01, p02, etc.
  novel_content_separator: "*** Content ***"

# Download settings
download:
  method: "direct"  # direct, aria2c, aria2rpc
  concurrency: 4    # Number of concurrent downloads
  delay_range: "1-3"  # Random delay between requests (seconds)
  conflict_strategy: "skip"  # skip, overwrite, rename
  timeout_minutes: 30  # Overall download timeout in minutes
  
  # Aria2 RPC settings (only used if method is aria2rpc)
  aria2:
    rpc_url: "ws://localhost:6800/jsonrpc"
    secret: ""  # Optional RPC secret
    verify_ssl: true
    ca_cert_path: ""  # Optional custom CA certificate

# HTTP settings
http:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  referer: "https://www.pixiv.net/"
  timeout_seconds: 30
  max_retries: 3
  retry_delay_seconds: 1
  verify_ssl: true
  
  # Custom headers (optional)
  headers:
    Accept-Language: "en-US,en;q=0.9"

# Logging settings
logging:
  level: "info"  # trace, debug, info, warn, error
  file: ""  # Optional log directory (empty = no file logging, filename auto-generated as YY-MM-DD_HH_MM_SS.log)
  console: true  # Enable console logging
